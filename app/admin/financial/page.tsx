"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { HeaderTabContent } from "@/components/ui/header-tab-content"
import {
  DollarSign,
  TrendingUp,
  Calculator,
  PieChart,
  BarChart3,
  Target,
  AlertTriangle,
  CheckCircle,
} from "lucide-react"
import { financialService } from "@/lib/modules/financial/services"
import type {
  FinancialAsset,
  FinancialMetrics,
  BudgetItem,
  TCOAnalysis,
  ROIAnalysis,
  LeaseVsBuyAnalysis,
  TaxOptimization,
} from "@/lib/modules/financial/types"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { getFinancialHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getFinancialHeaderTabs } from "@/lib/utils/admin-tabs-configs"

export default function FinancialManagementPage() {
  const [assets, setAssets] = useState<FinancialAsset[]>([])
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null)
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([])
  const [selectedAsset, setSelectedAsset] = useState<FinancialAsset | null>(null)
  const [tcoAnalysis, setTcoAnalysis] = useState<TCOAnalysis | null>(null)
  const [roiAnalysis, setRoiAnalysis] = useState<ROIAnalysis | null>(null)
  const [leaseVsBuyAnalysis, setLeaseVsBuyAnalysis] = useState<LeaseVsBuyAnalysis | null>(null)
  const [taxOptimization, setTaxOptimization] = useState<TaxOptimization | null>(null)
  const [loading, setLoading] = useState(true)

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => getFinancialHeaderConfig(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getFinancialHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Asset Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Asset Portfolio</CardTitle>
                  <CardDescription>Select an asset for detailed analysis</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {assets.map((asset) => (
                    <div
                      key={asset.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedAsset?.id === asset.id
                          ? "border-primary bg-primary/10"
                          : "border-border hover:border-border/80"
                      }`}
                      onClick={() => handleAssetSelect(asset)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold">{asset.name}</h3>
                          <p className="text-sm text-muted-foreground">{asset.assetType}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{formatCurrency(asset.currentValue)}</p>
                          <Badge variant={asset.status === "Active" ? "default" : "secondary"}>
                            {asset.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common financial analysis tasks</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => selectedAsset && handleTCOAnalysis(selectedAsset)}
                    disabled={!selectedAsset}
                  >
                    <Calculator className="mr-2 h-4 w-4" />
                    Calculate TCO
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => selectedAsset && handleROIAnalysis(selectedAsset)}
                    disabled={!selectedAsset}
                  >
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Analyze ROI
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => selectedAsset && handleLeaseVsBuyAnalysis(selectedAsset)}
                    disabled={!selectedAsset}
                  >
                    <PieChart className="mr-2 h-4 w-4" />
                    Lease vs Buy
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => selectedAsset && handleTaxOptimization(selectedAsset)}
                    disabled={!selectedAsset}
                  >
                    <Target className="mr-2 h-4 w-4" />
                    Optimize Tax Strategy
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        );
      case "tco":
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Total Cost of Ownership Analysis</CardTitle>
                <CardDescription>Comprehensive cost breakdown over asset lifecycle</CardDescription>
              </CardHeader>
              <CardContent>
                {selectedAsset ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Selected Asset:</span>
                      <span>{selectedAsset.name}</span>
                    </div>
                    <Button onClick={runTCOAnalysis} disabled={!selectedAsset}>
                      <Calculator className="mr-2 h-4 w-4" />
                      Calculate TCO
                    </Button>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Select an asset to perform TCO analysis</p>
                )}
                {tcoAnalysis && (
                  <div className="mt-6 space-y-4">
                    <h3 className="text-lg font-semibold">TCO Analysis Results</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Initial Investment</p>
                        <p className="text-2xl font-bold">{formatCurrency(tcoAnalysis.initialInvestment)}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Total Operating Costs</p>
                        <p className="text-2xl font-bold">{formatCurrency(tcoAnalysis.totalOperatingCosts)}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );
      case "roi":
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Return on Investment Analysis</CardTitle>
                <CardDescription>Investment performance and profitability metrics</CardDescription>
              </CardHeader>
              <CardContent>
                {selectedAsset ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Selected Asset:</span>
                      <span>{selectedAsset.name}</span>
                    </div>
                    <Button onClick={runROIAnalysis} disabled={!selectedAsset}>
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Calculate ROI
                    </Button>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Select an asset to perform ROI analysis</p>
                )}
                {roiAnalysis && (
                  <div className="mt-6 space-y-4">
                    <h3 className="text-lg font-semibold">ROI Analysis Results</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Initial Investment</p>
                        <p className="text-2xl font-bold">{formatCurrency(roiAnalysis.initialInvestment)}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">ROI Percentage</p>
                        <p className="text-3xl font-bold text-primary">{formatPercentage(roiAnalysis.roiPercentage)}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );
      case "lease-buy":
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Lease vs Buy Analysis</CardTitle>
                <CardDescription>Compare financing options to optimize your investment</CardDescription>
              </CardHeader>
              <CardContent>
                {selectedAsset ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Selected Asset:</span>
                      <span>{selectedAsset.name}</span>
                    </div>
                    <Button onClick={runLeaseVsBuyAnalysis} disabled={!selectedAsset}>
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Compare Options
                    </Button>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Select an asset to perform lease vs buy analysis</p>
                )}
                {leaseVsBuyAnalysis && (
                  <div className="mt-6 space-y-4">
                    <h3 className="text-lg font-semibold">Analysis Results</h3>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        Recommendation: {leaseVsBuyAnalysis.recommendation}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );
      case "tax":
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Tax Optimization Strategy</CardTitle>
                <CardDescription>Maximize tax benefits through strategic depreciation</CardDescription>
              </CardHeader>
              <CardContent>
                {selectedAsset ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Selected Asset:</span>
                      <span>{selectedAsset.name}</span>
                    </div>
                    <Button onClick={runTaxOptimization} disabled={!selectedAsset}>
                      <PieChart className="mr-2 h-4 w-4" />
                      Optimize Tax Strategy
                    </Button>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Select an asset to perform tax optimization</p>
                )}
                {taxOptimization && (
                  <div className="mt-6 space-y-4">
                    <h3 className="text-lg font-semibold">Tax Optimization Results</h3>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        Total Tax Savings: {formatCurrency(taxOptimization.totalTaxSavings)}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );
      case "budget":
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Budget Management</CardTitle>
                <CardDescription>Track budget performance and variance analysis</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {budgetItems.map((item) => (
                    <Card key={item.id} className="border-l-4 border-l-blue-500">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">{item.category}</CardTitle>
                        <CardDescription>{item.description}</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span>Budgeted:</span>
                          <span className="font-semibold">{formatCurrency(item.budgetedAmount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Actual:</span>
                          <span className="font-semibold">{formatCurrency(item.actualAmount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Variance:</span>
                          <span
                            className={`font-semibold ${
                              item.variance >= 0 ? "text-green-600" : "text-red-600"
                            }`}
                          >
                            {formatCurrency(Math.abs(item.variance))} {item.variance >= 0 ? "under" : "over"}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{formatPercentage(item.utilizationPercentage)}</span>
                          </div>
                          <Progress value={item.utilizationPercentage} className="h-2" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        const assetsData = financialService.getAssets()
        const metricsData = financialService.getFinancialMetrics()
        const budgetData = financialService.getBudgetItems()

        setAssets(assetsData)
        setMetrics(metricsData)
        setBudgetItems(budgetData)

        if (assetsData.length > 0) {
          setSelectedAsset(assetsData[0])
        }
      } catch (error) {
        console.error("Error loading financial data:", error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const handleAssetSelect = (asset: FinancialAsset) => {
    setSelectedAsset(asset)
    setTcoAnalysis(null)
    setRoiAnalysis(null)
    setLeaseVsBuyAnalysis(null)
    setTaxOptimization(null)
  }

  const runTCOAnalysis = () => {
    if (selectedAsset) {
      const analysis = financialService.calculateTCO(selectedAsset.id)
      setTcoAnalysis(analysis)
    }
  }

  const runROIAnalysis = () => {
    if (selectedAsset) {
      // Sample cash flows for demonstration
      const cashFlows = Array(selectedAsset.usefulLife).fill(selectedAsset.acquisitionCost * 0.2)
      const analysis = financialService.calculateROI(selectedAsset.id, cashFlows)
      setRoiAnalysis(analysis)
    }
  }

  const runLeaseVsBuyAnalysis = () => {
    if (selectedAsset) {
      const analysis = financialService.analyzeLeaseVsBuy(selectedAsset.id)
      setLeaseVsBuyAnalysis(analysis)
    }
  }

  const runTaxOptimization = () => {
    if (selectedAsset) {
      const optimization = financialService.optimizeTaxStrategy(selectedAsset.id)
      setTaxOptimization(optimization)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">

      {/* Key Metrics Cards */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Asset Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(metrics.totalAssetValue)}</div>
              <p className="text-xs text-muted-foreground">
                Portfolio health: {formatPercentage(metrics.portfolioHealth)}
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average ROI</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPercentage(metrics.averageROI)}</div>
              <p className="text-xs text-muted-foreground">Asset turnover: {metrics.assetTurnover.toFixed(2)}x</p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPercentage(metrics.budgetUtilization)}</div>
              <Progress value={metrics.budgetUtilization} className="mt-2" />
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tax Savings</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(metrics.taxSavings)}</div>
              <p className="text-xs text-muted-foreground">
                Maintenance ratio: {formatPercentage(metrics.maintenanceCostRatio)}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "tco", content: getTabContent("tco") },
          { id: "roi", content: getTabContent("roi") },
          { id: "lease-buy", content: getTabContent("lease-buy") },
          { id: "tax", content: getTabContent("tax") },
          { id: "budget", content: getTabContent("budget") },
        ]}
      />
    </div>
  )
}
