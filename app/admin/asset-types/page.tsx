"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AdminTabsRenderer } from "@/components/ui/admin-tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Plus,
  Setting<PERSON>,
  Edit,
  Trash2,
  Co<PERSON>,
  Eye,
  Layers,
  Workflow,
  BarChart3,
  Search,
  Filter,
  Download,
  Upload,
  Wrench,
  Clock,
  TrendingDown,
  CheckCircle,
} from "lucide-react"
import { AssetTypeService } from "@/lib/modules/asset-types/services"
import type { AssetType, AssetTypeMetrics } from "@/lib/modules/asset-types/types"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getAssetTypesHeaderConfig } from "@/lib/utils/admin-header-configs"
import { AssetTypeTemplateSelector } from "@/components/asset-types/asset-type-template-selector"
import { AssetTypeTemplate } from "@/lib/templates/asset-type-templates"
import { AssetTypeForm as AssetTypeFormComponent } from "@/components/asset-types/asset-type-form"

function getStatusBadge(isActive: boolean) {
  return isActive ? (
    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
      <CheckCircle className="w-3 h-3 mr-1" />
      Active
    </Badge>
  ) : (
    <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
      <Clock className="w-3 h-3 mr-1" />
      Inactive
    </Badge>
  )
}

function TemplateLibrary({ onTemplateSelect }: { onTemplateSelect: (template: AssetTypeTemplate, customizations?: any) => void }) {
  const [templates, setTemplates] = useState<AssetTypeTemplate[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/asset-type-templates")
      if (response.ok) {
        const data = await response.json()
        setTemplates(data.templates)
      }
    } catch (error) {
      console.error("Error loading templates:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getTemplateIcon = (template: AssetTypeTemplate) => {
    switch (template.icon) {
      case "Laptop":
        return <Layers className="h-6 w-6" />
      case "Table":
        return <Layers className="h-6 w-6" />
      case "Car":
        return <Layers className="h-6 w-6" />
      default:
        return <Layers className="h-6 w-6" />
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading templates...</span>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {templates.map((template) => (
        <Card
          key={template.id}
          className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
          onClick={() => onTemplateSelect(template)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: `${template.color}20`, color: template.color }}
              >
                {getTemplateIcon(template)}
              </div>
              <div className="flex-1">
                <CardTitle className="text-sm">{template.name}</CardTitle>
                <Badge variant="outline" className="text-xs">
                  {template.category}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm text-muted-foreground mb-3">
              {template.description}
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Custom Fields:</span>
                <Badge variant="secondary">{template.customFields.length}</Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Lifecycle Stages:</span>
                <Badge variant="secondary">{template.lifecycleStages.length}</Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Maintenance Schedules:</span>
                <Badge variant="secondary">{template.maintenanceSchedules.length}</Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-1 mt-3">
              {template.tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export default function AssetTypesPage() {
  const [assetTypes, setAssetTypes] = useState<AssetType[]>([])
  const [metrics, setMetrics] = useState<AssetTypeMetrics | null>(null)
  const [selectedAssetType, setSelectedAssetType] = useState<AssetType | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [activeTab, setActiveTab] = useState("overview")

  const assetTypeService = AssetTypeService.getInstance()

  // Set up the header for this page
  useAdminHeader(() => getAssetTypesHeaderConfig(setIsCreateDialogOpen))

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    const types = assetTypeService.getAssetTypes()
    const metricsData = await assetTypeService.getMetrics()
    setAssetTypes(types)
    setMetrics(metricsData)
  }

  const handleTemplateSelect = async (template: AssetTypeTemplate, customizations?: any) => {
    try {
      const response = await fetch("/api/asset-type-templates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: template.id,
          customizations,
          createForms: true,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Asset type created from template:", result);
        setIsTemplateDialogOpen(false);
        await loadData(); // Refresh the data
      } else {
        const error = await response.json();
        console.error("Failed to create asset type from template:", error);
      }
    } catch (error) {
      console.error("Error creating asset type from template:", error);
    }
  }

  const filteredAssetTypes = assetTypes.filter((type) => {
    const matchesSearch =
      type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      type.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      type.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "All" || type.category.name === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = ["All", ...Array.from(new Set(assetTypes.map((type) => type.category.name)))]

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Asset Type</DialogTitle>
            <DialogDescription>Define a new asset type with custom fields and configuration</DialogDescription>
          </DialogHeader>
          <AssetTypeFormComponent
            onSave={() => {
              setIsCreateDialogOpen(false)
              loadData()
            }}
          />
        </DialogContent>
      </Dialog>

      <AssetTypeTemplateSelector
        isOpen={isTemplateDialogOpen}
        onOpenChange={setIsTemplateDialogOpen}
        onTemplateSelect={handleTemplateSelect}
        onCancel={() => setIsTemplateDialogOpen(false)}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="types">Asset Types</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Asset Types</CardTitle>
                <Layers className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics?.totalTypes || 0}</div>
                <p className="text-xs text-muted-foreground">{metrics?.activeTypes || 0} active</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Custom Fields</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Object.values(metrics?.customFieldsUsage || {}).reduce((a, b) => a + b, 0)}
                </div>
                <p className="text-xs text-muted-foreground">Across all types</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Maintenance Schedules</CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics?.maintenanceScheduleCount || 0}</div>
                <p className="text-xs text-muted-foreground">Active schedules</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500" />
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Lifecycle Stages</CardTitle>
                <Workflow className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Object.values(metrics?.lifecycleDistribution || {}).reduce((a, b) => a + b, 0)}
                </div>
                <p className="text-xs text-muted-foreground">Total stages</p>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-500 to-blue-500" />
              </CardContent>
            </Card>
          </div>

          {/* Quick Stats */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Depreciation Methods</CardTitle>
                <CardDescription>Distribution of depreciation methods in use</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(metrics?.depreciationMethods || {}).map(([method, count]) => (
                    <div key={method} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TrendingDown className="h-4 w-4 text-muted-foreground" />
                        <span className="capitalize">{method.replace("_", " ")}</span>
                      </div>
                      <Badge variant="outline">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Custom Field Types</CardTitle>
                <CardDescription>Most commonly used field types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(metrics?.customFieldsUsage || {}).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Settings className="h-4 w-4 text-muted-foreground" />
                        <span className="capitalize">{type}</span>
                      </div>
                      <Badge variant="outline">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="types" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Asset Types</CardTitle>
                  <CardDescription>Manage your asset type definitions and configurations</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search asset types..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8 w-64"
                    />
                  </div>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Asset Type</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Code</TableHead>
                      <TableHead>Custom Fields</TableHead>
                      <TableHead>Lifecycle Stages</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAssetTypes.map((type) => (
                      <TableRow key={type.id} className="hover:bg-muted/50">
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div
                              className="w-10 h-10 rounded-lg flex items-center justify-center text-white"
                              style={{ backgroundColor: type.color }}
                            >
                              <Layers className="h-5 w-5" />
                            </div>
                            <div>
                              <p className="font-medium">{type.name}</p>
                              <p className="text-sm text-muted-foreground">{type.description}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{type.category.name}</Badge>
                        </TableCell>
                        <TableCell className="font-mono text-sm">{type.code}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Settings className="h-4 w-4 text-muted-foreground" />
                            <span>{type.customFields.length}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Workflow className="h-4 w-4 text-muted-foreground" />
                            <span>{type.lifecycleStages.length}</span>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(type.isActive)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedAssetType(type)
                                setIsEditDialogOpen(true)
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Asset Categories</CardTitle>
              <CardDescription>Organize asset types into logical categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <Layers className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg text-muted-foreground mb-2">Category Management</p>
                  <p className="text-sm text-muted-foreground">Hierarchical organization of asset types</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Asset Type Templates</CardTitle>
                  <CardDescription>Pre-configured templates for common asset types</CardDescription>
                </div>
                <Button onClick={() => setIsTemplateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Use Template
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <TemplateLibrary onTemplateSelect={handleTemplateSelect} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Usage Analytics</CardTitle>
                <CardDescription>Asset type usage and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Usage Analytics</p>
                    <p className="text-sm text-muted-foreground">Asset type utilization trends</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Configuration Health</CardTitle>
                <CardDescription>Asset type configuration completeness</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Configuration Health</p>
                    <p className="text-sm text-muted-foreground">Completeness and validation status</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Asset Type Detail Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Asset Type Details</DialogTitle>
            <DialogDescription>View and edit asset type configuration</DialogDescription>
          </DialogHeader>
          {selectedAssetType && (
            <AssetTypeDetailView
              assetType={selectedAssetType}
              onSave={() => {
                setIsEditDialogOpen(false)
                loadData()
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Asset Type Form Component
function AssetTypeForm({ onSave }: { onSave: () => void }) {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    category: "",
    color: "#3B82F6",
    isActive: true,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // Implementation would create the asset type
    onSave()
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Asset Type Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Enter asset type name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="code">Code *</Label>
          <Input
            id="code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
            placeholder="Enter unique code"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Describe this asset type..."
          rows={3}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="it">IT Equipment</SelectItem>
              <SelectItem value="vehicles">Vehicles</SelectItem>
              <SelectItem value="machinery">Machinery</SelectItem>
              <SelectItem value="furniture">Furniture</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="color">Color</Label>
          <Input
            id="color"
            type="color"
            value={formData.color}
            onChange={(e) => setFormData({ ...formData, color: e.target.value })}
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="isActive">Active</Label>
      </div>

      <DialogFooter>
        <Button type="submit">Create Asset Type</Button>
      </DialogFooter>
    </form>
  )
}

// Asset Type Detail View Component
function AssetTypeDetailView({ assetType, onSave }: { assetType: AssetType; onSave: () => void }) {
  return (
    <Tabs defaultValue="general" className="w-full">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="fields">Custom Fields</TabsTrigger>
        <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
        <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
      </TabsList>

      <TabsContent value="general" className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Name</Label>
            <Input value={assetType.name} readOnly />
          </div>
          <div className="space-y-2">
            <Label>Code</Label>
            <Input value={assetType.code} readOnly />
          </div>
        </div>
        <div className="space-y-2">
          <Label>Description</Label>
          <Textarea value={assetType.description} readOnly rows={3} />
        </div>
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Category</Label>
            <Input value={assetType.category.name} readOnly />
          </div>
          <div className="space-y-2">
            <Label>Color</Label>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 rounded border" style={{ backgroundColor: assetType.color }} />
              <Input value={assetType.color} readOnly />
            </div>
          </div>
          <div className="space-y-2">
            <Label>Status</Label>
            <div className="pt-2">{getStatusBadge(assetType.isActive)}</div>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="fields" className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Custom Fields ({assetType.customFields.length})</h3>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Field
          </Button>
        </div>
        <ScrollArea className="h-[400px]">
          <div className="space-y-4">
            {assetType.customFields.map((field) => (
              <Card key={field.id}>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{field.label}</p>
                        <Badge variant="outline">{field.type}</Badge>
                        {field.isRequired && <Badge variant="secondary">Required</Badge>}
                      </div>
                      <p className="text-sm text-muted-foreground">{field.description}</p>
                      <p className="text-xs text-muted-foreground">Field Name: {field.name}</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </TabsContent>

      <TabsContent value="lifecycle" className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Lifecycle Stages ({assetType.lifecycleStages.length})</h3>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Stage
          </Button>
        </div>
        <ScrollArea className="h-[400px]">
          <div className="space-y-4">
            {assetType.lifecycleStages
              .sort((a, b) => a.order - b.order)
              .map((stage, index) => (
                <Card key={stage.id}>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted text-sm font-medium">
                          {index + 1}
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <p className="font-medium">{stage.name}</p>
                            <Badge variant="outline" style={{ backgroundColor: stage.color, borderColor: stage.color }}>
                              {stage.code}
                            </Badge>
                            {stage.isInitial && <Badge variant="secondary">Initial</Badge>}
                            {stage.isFinal && <Badge variant="secondary">Final</Badge>}
                          </div>
                          <p className="text-sm text-muted-foreground">{stage.description}</p>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <span>Transitions to: {stage.allowedTransitions.length} stages</span>
                            <span>•</span>
                            <span>Required fields: {stage.requiredFields.length}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </ScrollArea>
      </TabsContent>

      <TabsContent value="maintenance" className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Maintenance Schedules ({assetType.maintenanceSchedules.length})</h3>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Schedule
          </Button>
        </div>
        <ScrollArea className="h-[400px]">
          <div className="space-y-4">
            {assetType.maintenanceSchedules.map((schedule) => (
              <Card key={schedule.id}>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{schedule.name}</p>
                        <Badge variant="outline">{schedule.type}</Badge>
                        <Badge
                          variant="outline"
                          className={
                            schedule.priority === "critical"
                              ? "border-red-500 text-red-700"
                              : schedule.priority === "high"
                                ? "border-orange-500 text-orange-700"
                                : schedule.priority === "medium"
                                  ? "border-yellow-500 text-yellow-700"
                                  : "border-green-500 text-green-700"
                          }
                        >
                          {schedule.priority}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{schedule.description}</p>
                      <div className="grid grid-cols-3 gap-4 text-xs text-muted-foreground">
                        <div>
                          <span className="font-medium">Frequency:</span> Every {schedule.frequency.interval}{" "}
                          {schedule.frequency.type}
                        </div>
                        <div>
                          <span className="font-medium">Duration:</span> {schedule.estimatedDuration} minutes
                        </div>
                        <div>
                          <span className="font-medium">Cost:</span> ${schedule.estimatedCost}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium">Checklist items:</span> {schedule.checklistItems.length}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </TabsContent>

      <TabsContent value="depreciation" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Depreciation Settings</CardTitle>
            <CardDescription>Configure how assets of this type depreciate over time</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Depreciation Method</Label>
                <Input value={assetType.depreciationSettings.method.replace("_", " ")} readOnly />
              </div>
              <div className="space-y-2">
                <Label>Useful Life</Label>
                <Input
                  value={`${assetType.depreciationSettings.usefulLife} ${assetType.depreciationSettings.usefulLifeUnit}`}
                  readOnly
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Salvage Value</Label>
                <Input
                  value={
                    assetType.depreciationSettings.salvageValueType === "percentage"
                      ? `${assetType.depreciationSettings.salvageValue}%`
                      : `$R{assetType.depreciationSettings.salvageValue}`
                  }
                  readOnly
                />
              </div>
              <div className="space-y-2">
                <Label>Start Date</Label>
                <Input value={new Date(assetType.depreciationSettings.startDate).toLocaleDateString()} readOnly />
              </div>
            </div>

            {/* Depreciation Calculator */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-base">Depreciation Calculator</CardTitle>
                <CardDescription>Calculate depreciation for a sample asset value</CardDescription>
              </CardHeader>
              <CardContent>
                <DepreciationCalculator depreciationSettings={assetType.depreciationSettings} />
              </CardContent>
            </Card>
          </CardContent>
        </Card>
      </TabsContent>

      <DialogFooter>
        <Button variant="outline" onClick={onSave}>
          Close
        </Button>
        <Button onClick={onSave}>Save Changes</Button>
      </DialogFooter>
    </Tabs>
  )
}

// Depreciation Calculator Component
function DepreciationCalculator({ depreciationSettings }: { depreciationSettings: any }) {
  const [assetValue, setAssetValue] = useState(10000)
  const [calculation, setCalculation] = useState<any>(null)
  const assetTypeService = AssetTypeService.getInstance()

  useEffect(() => {
    const result = assetTypeService.calculateDepreciation(assetValue, depreciationSettings)
    setCalculation(result)
  }, [assetValue, depreciationSettings])

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="assetValue">Asset Value ($)</Label>
        <Input
          id="assetValue"
          type="number"
          value={assetValue}
          onChange={(e) => setAssetValue(Number(e.target.value))}
          min="0"
          step="100"
        />
      </div>

      {calculation && (
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Annual Depreciation</p>
                <p className="text-2xl font-bold text-red-600">${calculation.annualDepreciation.toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Current Book Value</p>
                <p className="text-2xl font-bold text-green-600">${calculation.bookValue.toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Accumulated Depreciation</p>
                <p className="text-2xl font-bold text-orange-600">
                  ${calculation.accumulatedDepreciation.toLocaleString()}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Remaining Life</p>
                <p className="text-2xl font-bold text-blue-600">{calculation.remainingLife.toFixed(1)} years</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
