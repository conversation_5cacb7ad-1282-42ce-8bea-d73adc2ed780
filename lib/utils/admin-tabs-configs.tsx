import React from "react";
import {
  BarChart3,
  FileText,
  Settings,
  Users,
  Package,
  Calendar,
  TrendingUp,
  Brain,
  List,
  Eye,
  Activity,
  Wrench,
  DollarSign,
  ShoppingCart,
  Building,
  CreditCard,
  Database,
  AlertTriangle,
  CheckCircle,
  Clock,
  GitBranch,
  TrendingDown,
  Filter,
  Search,
  Target,
  Layers,
  Workflow,
  Zap,
  RefreshCw,
  Download,
  Upload,
  Plus,
} from "lucide-react";
import type { TabsConfig } from "@/store/admin-tabs-store";
import type { HeaderTabItem } from "@/store/app-header-store";

// Header Tab Configurations (for integrated header tabs)
export const getAssetManagementHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "list",
    label: "Asset List",
    icon: List,
  },
  {
    id: "overview",
    label: "Overview",
    icon: Eye,
  },
  {
    id: "analytics",
    label: "Analytics",
    icon: BarChart3,
  },
  {
    id: "ai-insights",
    label: "AI Insights",
    icon: Brain,
  },
];

export const getAssetTypesHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Eye,
  },
  {
    id: "types",
    label: "Asset Types",
    icon: Package,
  },
  {
    id: "categories",
    label: "Categories",
    icon: Layers,
  },
  {
    id: "templates",
    label: "Templates",
    icon: FileText,
  },
  {
    id: "analytics",
    label: "Analytics",
    icon: BarChart3,
  },
];

export const getFinancialHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Eye,
  },
  {
    id: "tco",
    label: "TCO Analysis",
    icon: BarChart3,
  },
  {
    id: "roi",
    label: "ROI Analysis",
    icon: TrendingUp,
  },
  {
    id: "lease-buy",
    label: "Lease vs Buy",
    icon: CreditCard,
  },
  {
    id: "tax",
    label: "Tax Optimization",
    icon: DollarSign,
  },
  {
    id: "budget",
    label: "Budget Management",
    icon: Target,
  },
];

export const getMaintenanceHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Eye,
  },
  {
    id: "scheduled",
    label: "Scheduled",
    icon: Calendar,
  },
  {
    id: "calendar",
    label: "Calendar",
    icon: Calendar,
  },
  {
    id: "technicians",
    label: "Technicians",
    icon: Users,
  },
  {
    id: "analytics",
    label: "Analytics",
    icon: BarChart3,
  },
];

export const getSettingsHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "general",
    label: "General",
    icon: Settings,
  },
  {
    id: "users",
    label: "Users & Roles",
    icon: Users,
  },
  {
    id: "customFields",
    label: "Custom Fields",
    icon: Database,
  },
  {
    id: "formBuilder",
    label: "Form Builder",
    icon: FileText,
  },
  {
    id: "notifications",
    label: "Notifications",
    icon: AlertTriangle,
  },
  {
    id: "integrations",
    label: "Integrations",
    icon: Workflow,
  },
  {
    id: "security",
    label: "Security",
    icon: CheckCircle,
  },
];

// Legacy Tabs Configuration (for standalone tabs)
export const getAssetManagementTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "list",
      label: "Asset List",
      icon: List,
    },
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
    {
      id: "ai-insights",
      label: "AI Insights",
      icon: Brain,
    },
  ],
  defaultTab: "list",
  variant: "pills",
  size: "md",
});

// Asset Types Tabs Configuration
export const getAssetTypesTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "types",
      label: "Asset Types",
      icon: Package,
    },
    {
      id: "categories",
      label: "Categories",
      icon: Layers,
    },
    {
      id: "templates",
      label: "Templates",
      icon: FileText,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
  ],
  defaultTab: "overview",
  variant: "pills",
  size: "md",
});

// Asset Type Detail Tabs Configuration
export const getAssetTypeDetailTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "forms",
      label: "Forms",
      icon: FileText,
    },
    {
      id: "depreciation",
      label: "Depreciation",
      icon: TrendingDown,
    },
    {
      id: "lifecycle",
      label: "Lifecycle",
      icon: GitBranch,
    },
    {
      id: "maintenance",
      label: "Maintenance",
      icon: Wrench,
    },
    {
      id: "fields",
      label: "Fields",
      icon: Settings,
    },
  ],
  defaultTab: "forms",
  variant: "pills",
  size: "md",
});

// Financial Management Tabs Configuration
export const getFinancialTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "tco",
      label: "TCO Analysis",
      icon: BarChart3,
    },
    {
      id: "roi",
      label: "ROI Analysis",
      icon: TrendingUp,
    },
    {
      id: "lease-buy",
      label: "Lease vs Buy",
      icon: CreditCard,
    },
    {
      id: "tax",
      label: "Tax Optimization",
      icon: DollarSign,
    },
    {
      id: "budget",
      label: "Budget Management",
      icon: Target,
    },
  ],
  defaultTab: "overview",
  variant: "underline",
  size: "md",
});

// Maintenance Tabs Configuration
export const getMaintenanceTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "scheduled",
      label: "Scheduled",
      icon: Calendar,
    },
    {
      id: "calendar",
      label: "Calendar",
      icon: Calendar,
    },
    {
      id: "technicians",
      label: "Technicians",
      icon: Users,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
  ],
  defaultTab: "overview",
  variant: "pills",
  size: "md",
});

// Settings Tabs Configuration
export const getSettingsTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "general",
      label: "General",
      icon: Settings,
    },
    {
      id: "users",
      label: "Users & Roles",
      icon: Users,
    },
    {
      id: "customFields",
      label: "Custom Fields",
      icon: Database,
    },
    {
      id: "formBuilder",
      label: "Form Builder",
      icon: FileText,
    },
    {
      id: "notifications",
      label: "Notifications",
      icon: AlertTriangle,
    },
    {
      id: "integrations",
      label: "Integrations",
      icon: Workflow,
    },
    {
      id: "security",
      label: "Security",
      icon: CheckCircle,
    },
  ],
  defaultTab: "general",
  variant: "default",
  size: "md",
});

// AI Demo Tabs Configuration
export const getAIDemoTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "functions",
      label: "AI Functions",
      icon: Brain,
    },
    {
      id: "insights",
      label: "Insights Dashboard",
      icon: BarChart3,
    },
    {
      id: "assistant",
      label: "AI Assistant",
      icon: Zap,
    },
  ],
  defaultTab: "functions",
  variant: "pills",
  size: "md",
});

// Inventory Tabs Configuration
export const getInventoryTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "items",
      label: "Items",
      icon: Package,
    },
    {
      id: "alerts",
      label: "Alerts",
      icon: AlertTriangle,
    },
    {
      id: "procurement",
      label: "Procurement",
      icon: ShoppingCart,
    },
    {
      id: "suppliers",
      label: "Suppliers",
      icon: Building,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
  ],
  defaultTab: "overview",
  variant: "pills",
  size: "md",
});

// Asset Leasing Tabs Configuration
export const getAssetLeasingTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "active",
      label: "Active Leases",
      icon: CheckCircle,
    },
    {
      id: "calendar",
      label: "Calendar View",
      icon: Calendar,
    },
    {
      id: "payments",
      label: "Payments",
      icon: CreditCard,
    },
    {
      id: "all",
      label: "All Leases",
      icon: List,
    },
  ],
  defaultTab: "active",
  variant: "pills",
  size: "md",
});

// Automation Tabs Configuration
export const getAutomationTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "workflows",
      label: "Workflows",
      icon: Workflow,
    },
    {
      id: "rules",
      label: "Rules",
      icon: Settings,
    },
    {
      id: "schedules",
      label: "Schedules",
      icon: Clock,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
  ],
  defaultTab: "workflows",
  variant: "pills",
  size: "md",
});

// E-commerce Tabs Configuration
export const getEcommerceTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "products",
      label: "Products",
      icon: Package,
    },
    {
      id: "orders",
      label: "Orders",
      icon: ShoppingCart,
    },
    {
      id: "customers",
      label: "Customers",
      icon: Users,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
  ],
  defaultTab: "products",
  variant: "default",
  size: "md",
});

// CRM Tabs Configuration
export const getCRMTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "contacts",
      label: "Contacts",
      icon: Users,
    },
    {
      id: "deals",
      label: "Deals",
      icon: DollarSign,
    },
    {
      id: "activities",
      label: "Activities",
      icon: Activity,
    },
    {
      id: "campaigns",
      label: "Campaigns",
      icon: Target,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
  ],
  defaultTab: "overview",
  variant: "pills",
  size: "md",
});

// Asset Detail Tabs Configuration
export const getAssetDetailTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "details",
      label: "Details",
      icon: FileText,
    },
    {
      id: "operations",
      label: "Operations",
      icon: Activity,
    },
    {
      id: "financial",
      label: "Financial",
      icon: DollarSign,
    },
  ],
  defaultTab: "overview",
  variant: "pills",
  size: "md",
});

// Asset Automation Tabs Configuration
export const getAssetAutomationTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: Eye,
    },
    {
      id: "templates",
      label: "Templates",
      icon: FileText,
    },
    {
      id: "recent",
      label: "Recent Workflows",
      icon: Clock,
    },
  ],
  defaultTab: "overview",
  variant: "default",
  size: "md",
});

// Asset Operations Dashboard Tabs Configuration
export const getAssetOperationsTabsConfig = (): TabsConfig => ({
  tabs: [
    {
      id: "history",
      label: "Operation History",
      icon: Clock,
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: BarChart3,
    },
    {
      id: "workflows",
      label: "Workflows",
      icon: Workflow,
    },
  ],
  defaultTab: "history",
  variant: "default",
  size: "md",
});
